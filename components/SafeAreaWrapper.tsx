/**
 * Safe Area Wrapper Component
 * Uses our polyfilled react-native-safe-area-context for compatibility
 */

import React from 'react';
import { SafeAreaProvider, useSafeAreaInsets } from 'react-native-safe-area-context';

// Re-export the hook for compatibility
export { useSafeAreaInsets };

// Main wrapper component
interface SafeAreaWrapperProps {
  children: React.ReactNode;
}

export function SafeAreaWrapper({ children }: SafeAreaWrapperProps) {
  return <SafeAreaProvider>{children}</SafeAreaProvider>;
}

export default SafeAreaWrapper;
