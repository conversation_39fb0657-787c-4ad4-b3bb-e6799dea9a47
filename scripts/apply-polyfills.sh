#!/bin/bash

# Script to apply polyfills for problematic React Native packages
# This replaces native components with JavaScript-only implementations

echo "Applying polyfills for react-native-safe-area-context..."

# Copy polyfill files
cp polyfills/react-native-safe-area-context.js node_modules/react-native-safe-area-context/lib/module/polyfill.js
cp polyfills/react-native-safe-area-context.js node_modules/react-native-safe-area-context/lib/commonjs/polyfill.js

# Replace native component with View
cat > node_modules/react-native-safe-area-context/lib/module/specs/NativeSafeAreaView.js << 'EOF'
// Polyfill: Replace native component with a simple View
import { View } from 'react-native';
export default View;
//# sourceMappingURL=NativeSafeAreaView.js.map
EOF

# Replace module index to use polyfill
cat > node_modules/react-native-safe-area-context/lib/module/index.js << 'EOF'
'use client';

// Polyfill: Use our custom implementation
export * from './polyfill.js';
//# sourceMappingURL=index.js.map
EOF

# Replace CommonJS index to use polyfill
cat > node_modules/react-native-safe-area-context/lib/commonjs/index.js << 'EOF'
"use strict";
'use client';

// Polyfill: Use our custom implementation
var polyfill = require("./polyfill.js");

Object.defineProperty(exports, "__esModule", {
  value: true
});

// Export all polyfill exports
Object.keys(polyfill).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return polyfill[key];
    }
  });
});

// Also export default
if (polyfill.default) {
  exports.default = polyfill.default;
}

//# sourceMappingURL=index.js.map
EOF

echo "Applying polyfills for react-native-svg..."

# Copy SVG polyfill files
cp polyfills/react-native-svg.js node_modules/react-native-svg/lib/module/polyfill.js
cp polyfills/react-native-svg.js node_modules/react-native-svg/lib/commonjs/polyfill.js

# Replace problematic native components
cat > node_modules/react-native-svg/lib/module/fabric/CircleNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Circle as default } from '../polyfill.js';
//# sourceMappingURL=CircleNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/DefsNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Defs as default } from '../polyfill.js';
//# sourceMappingURL=DefsNativeComponent.js.map
EOF

# Replace other common problematic components
cat > node_modules/react-native-svg/lib/module/fabric/RectNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Rect as default } from '../polyfill.js';
//# sourceMappingURL=RectNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/PathNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Path as default } from '../polyfill.js';
//# sourceMappingURL=PathNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/LineNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Line as default } from '../polyfill.js';
//# sourceMappingURL=LineNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/GNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { G as default } from '../polyfill.js';
//# sourceMappingURL=GNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/ClipPathNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { ClipPath as default } from '../polyfill.js';
//# sourceMappingURL=ClipPathNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/TextNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Text as default } from '../polyfill.js';
//# sourceMappingURL=TextNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/EllipseNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Ellipse as default } from '../polyfill.js';
//# sourceMappingURL=EllipseNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/PolygonNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Polygon as default } from '../polyfill.js';
//# sourceMappingURL=PolygonNativeComponent.js.map
EOF

cat > node_modules/react-native-svg/lib/module/fabric/PolylineNativeComponent.js << 'EOF'
// Polyfill: Replace native component with polyfill
export { Polyline as default } from '../polyfill.js';
//# sourceMappingURL=PolylineNativeComponent.js.map
EOF

# Also handle react-native-screens issue
echo "Applying polyfills for react-native-screens..."

cat > node_modules/react-native-screens/lib/module/fabric/SearchBarNativeComponent.js << 'EOF'
// Polyfill: Replace native component with View
import { View } from 'react-native';
export default View;
//# sourceMappingURL=SearchBarNativeComponent.js.map
EOF

echo "Polyfills applied successfully!"
echo "Note: You'll need to run this script again after 'npm install' or 'npm ci'"
