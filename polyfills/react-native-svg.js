/**
 * Polyfill for react-native-svg
 * This replaces the problematic native SVG components with pure JavaScript implementations
 * using React Native's built-in components for basic shapes and icons
 */

import React from 'react';
import { View, Text } from 'react-native';

// Basic SVG container component
function Svg({ width = 24, height = 24, viewBox, children, style, ...props }) {
  const containerStyle = {
    width: typeof width === 'string' ? width : width,
    height: typeof height === 'string' ? height : height,
    overflow: 'hidden',
    ...style,
  };

  return (
    <View style={containerStyle} {...props}>
      {children}
    </View>
  );
}

// Circle component replacement
function Circle({ cx = 0, cy = 0, r = 0, fill = 'transparent', stroke = 'black', strokeWidth = 1, ...props }) {
  const size = r * 2;
  const circleStyle = {
    position: 'absolute',
    left: cx - r,
    top: cy - r,
    width: size,
    height: size,
    borderRadius: r,
    backgroundColor: fill !== 'transparent' ? fill : 'transparent',
    borderWidth: stroke !== 'transparent' ? strokeWidth : 0,
    borderColor: stroke,
  };

  return <View style={circleStyle} {...props} />;
}

// Rectangle component replacement
function Rect({ x = 0, y = 0, width = 0, height = 0, fill = 'transparent', stroke = 'black', strokeWidth = 1, rx = 0, ...props }) {
  const rectStyle = {
    position: 'absolute',
    left: x,
    top: y,
    width,
    height,
    backgroundColor: fill !== 'transparent' ? fill : 'transparent',
    borderWidth: stroke !== 'transparent' ? strokeWidth : 0,
    borderColor: stroke,
    borderRadius: rx,
  };

  return <View style={rectStyle} {...props} />;
}

// Line component replacement
function Line({ x1 = 0, y1 = 0, x2 = 0, y2 = 0, stroke = 'black', strokeWidth = 1, ...props }) {
  const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
  
  const lineStyle = {
    position: 'absolute',
    left: x1,
    top: y1,
    width: length,
    height: strokeWidth,
    backgroundColor: stroke,
    transformOrigin: '0 50%',
    transform: [{ rotate: `${angle}deg` }],
  };

  return <View style={lineStyle} {...props} />;
}

// Path component replacement (simplified)
function Path({ d, fill = 'transparent', stroke = 'black', strokeWidth = 1, ...props }) {
  // For simple paths, we'll render a basic shape
  // This is a simplified implementation - complex paths won't render correctly
  const pathStyle = {
    width: 20,
    height: 20,
    backgroundColor: fill !== 'transparent' ? fill : 'transparent',
    borderWidth: stroke !== 'transparent' ? strokeWidth : 0,
    borderColor: stroke,
  };

  return <View style={pathStyle} {...props} />;
}

// Text component replacement
function SvgText({ x = 0, y = 0, fontSize = 14, fill = 'black', children, ...props }) {
  const textStyle = {
    position: 'absolute',
    left: x,
    top: y - fontSize, // Adjust for baseline
    fontSize,
    color: fill,
  };

  return (
    <Text style={textStyle} {...props}>
      {children}
    </Text>
  );
}

// Group component replacement
function G({ children, transform, ...props }) {
  return (
    <View style={{ position: 'relative' }} {...props}>
      {children}
    </View>
  );
}

// Defs component replacement (for definitions)
function Defs({ children, ...props }) {
  // Defs are typically not rendered, just return null
  return null;
}

// LinearGradient component replacement
function LinearGradient({ id, x1, y1, x2, y2, children, ...props }) {
  // For now, just return null as gradients are complex to implement
  return null;
}

// Stop component replacement
function Stop({ offset, stopColor, stopOpacity, ...props }) {
  return null;
}

// ClipPath component replacement
function ClipPath({ id, children, ...props }) {
  return null;
}

// Mask component replacement
function Mask({ id, children, ...props }) {
  return null;
}

// Pattern component replacement
function Pattern({ id, children, ...props }) {
  return null;
}

// Polygon component replacement
function Polygon({ points, fill = 'transparent', stroke = 'black', strokeWidth = 1, ...props }) {
  const polygonStyle = {
    width: 20,
    height: 20,
    backgroundColor: fill !== 'transparent' ? fill : 'transparent',
    borderWidth: stroke !== 'transparent' ? strokeWidth : 0,
    borderColor: stroke,
  };

  return <View style={polygonStyle} {...props} />;
}

// Polyline component replacement
function Polyline({ points, fill = 'transparent', stroke = 'black', strokeWidth = 1, ...props }) {
  const polylineStyle = {
    width: 20,
    height: 20,
    backgroundColor: fill !== 'transparent' ? fill : 'transparent',
    borderWidth: stroke !== 'transparent' ? strokeWidth : 0,
    borderColor: stroke,
  };

  return <View style={polylineStyle} {...props} />;
}

// Ellipse component replacement
function Ellipse({ cx = 0, cy = 0, rx = 0, ry = 0, fill = 'transparent', stroke = 'black', strokeWidth = 1, ...props }) {
  const ellipseStyle = {
    position: 'absolute',
    left: cx - rx,
    top: cy - ry,
    width: rx * 2,
    height: ry * 2,
    borderRadius: rx,
    backgroundColor: fill !== 'transparent' ? fill : 'transparent',
    borderWidth: stroke !== 'transparent' ? strokeWidth : 0,
    borderColor: stroke,
  };

  return <View style={ellipseStyle} {...props} />;
}

// Export all SVG components
export {
  Svg,
  Circle,
  Rect,
  Line,
  Path,
  SvgText as Text,
  G,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Mask,
  Pattern,
  Polygon,
  Polyline,
  Ellipse,
};

// Default export
export default Svg;

// Additional exports for compatibility
export const SvgXml = ({ xml, ...props }) => {
  return <View {...props} />;
};

export const SvgUri = ({ uri, ...props }) => {
  return <View {...props} />;
};

export const SvgCss = ({ xml, ...props }) => {
  return <View {...props} />;
};

export const SvgCssUri = ({ uri, ...props }) => {
  return <View {...props} />;
};

// Native component exports (these are the ones causing the error)
export const CircleNativeComponent = Circle;
export const RectNativeComponent = Rect;
export const LineNativeComponent = Line;
export const PathNativeComponent = Path;
export const TextNativeComponent = SvgText;
export const GNativeComponent = G;
export const DefsNativeComponent = Defs;
export const LinearGradientNativeComponent = LinearGradient;
export const StopNativeComponent = Stop;
export const ClipPathNativeComponent = ClipPath;
export const MaskNativeComponent = Mask;
export const PatternNativeComponent = Pattern;
export const PolygonNativeComponent = Polygon;
export const PolylineNativeComponent = Polyline;
export const EllipseNativeComponent = Ellipse;
