/**
 * Polyfill for react-native-safe-area-context
 * This replaces the problematic native components with pure JavaScript implementations
 * using Expo's built-in functionality
 */

import React, { useMemo, createContext, useContext } from 'react';
import { View, Platform, StatusBar, Dimensions } from 'react-native';
import Constants from 'expo-constants';

// Safe area insets type
const SafeAreaInsets = {
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
};

// Calculate safe area insets using Expo's built-in functionality
function calculateSafeAreaInsets() {
  const { width, height } = Dimensions.get('window');
  
  // Get status bar height
  const statusBarHeight = Platform.OS === 'android' 
    ? StatusBar.currentHeight || 24 
    : Constants.statusBarHeight || 44;

  // Calculate bottom inset for devices with home indicator
  let bottomInset = 0;
  if (Platform.OS === 'ios') {
    // For iOS devices with home indicator (iPhone X and newer)
    // We can detect this by checking if the screen height suggests a modern iPhone
    const isModernIPhone = height >= 812; // iPhone X and newer
    bottomInset = isModernIPhone ? 34 : 0;
  }

  return {
    top: statusBarHeight,
    bottom: bottomInset,
    left: 0,
    right: 0,
  };
}

// Create context for safe area insets
const SafeAreaContext = createContext(SafeAreaInsets);

// Hook to get safe area insets
function useSafeAreaInsets() {
  const contextInsets = useContext(SafeAreaContext);
  
  return useMemo(() => {
    // If we have context insets, use them, otherwise calculate
    if (contextInsets && (contextInsets.top > 0 || contextInsets.bottom > 0)) {
      return contextInsets;
    }
    return calculateSafeAreaInsets();
  }, [contextInsets]);
}

// Safe Area Provider component
function SafeAreaProvider({ children, initialMetrics }) {
  const insets = useMemo(() => {
    if (initialMetrics && initialMetrics.insets) {
      return initialMetrics.insets;
    }
    return calculateSafeAreaInsets();
  }, [initialMetrics]);

  return (
    <SafeAreaContext.Provider value={insets}>
      <View style={{ flex: 1 }}>
        {children}
      </View>
    </SafeAreaContext.Provider>
  );
}

// Safe Area View component (alternative to the native one)
function SafeAreaView({ children, style, edges, ...props }) {
  const insets = useSafeAreaInsets();
  
  // Apply insets based on edges prop
  const edgesToApply = edges || ['top', 'bottom', 'left', 'right'];
  const paddingStyle = {
    paddingTop: edgesToApply.includes('top') ? insets.top : 0,
    paddingBottom: edgesToApply.includes('bottom') ? insets.bottom : 0,
    paddingLeft: edgesToApply.includes('left') ? insets.left : 0,
    paddingRight: edgesToApply.includes('right') ? insets.right : 0,
  };

  return (
    <View style={[paddingStyle, style]} {...props}>
      {children}
    </View>
  );
}

// Initial window metrics (for compatibility)
const initialWindowMetrics = {
  insets: calculateSafeAreaInsets(),
  frame: {
    x: 0,
    y: 0,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  },
};

// Export all the components and hooks that react-native-safe-area-context provides
export {
  SafeAreaProvider,
  SafeAreaView,
  useSafeAreaInsets,
  initialWindowMetrics,
  SafeAreaContext,
};

// Default export for compatibility
export default {
  SafeAreaProvider,
  SafeAreaView,
  useSafeAreaInsets,
  initialWindowMetrics,
  SafeAreaContext,
};
