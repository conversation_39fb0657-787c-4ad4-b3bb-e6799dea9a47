module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Support for React Native Reanimated (must be last)
      'react-native-reanimated/plugin',
    ],
    env: {
      production: {
        plugins: [
          // Ensure native modules are properly handled in production
          'react-native-reanimated/plugin',
        ],
      },
    },
  };
};
